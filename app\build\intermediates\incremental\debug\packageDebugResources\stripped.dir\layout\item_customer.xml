<?xml version="1.0" encoding="utf-8"?>
    
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="0dp"
        tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/_10sdp"
        android:paddingVertical="@dimen/_10sdp"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvFullName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Full Namea  ajkskk akja kakjashajs ajsja"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_weight="0.6"
                android:layout_centerVertical="true"
                android:textColor="@color/profit_black"/>

            <TextView
                android:id="@+id/tvEmail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="@dimen/_200sdp"
                android:text="Full Name ajsjahaj a ashas"
                android:textAlignment="textEnd"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:layout_weight="0.4"
                android:textFontWeight="400"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="@color/profit_black"/>


        </LinearLayout>

    </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
    
    