<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:maxWidth="@dimen/_400sdp"
        android:fitsSystemWindows="true"
    android:layout_centerHorizontal="true"
        android:animateLayoutChanges="true"
        android:id="@+id/activity_main">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_height="match_parent">


            <androidx.appcompat.widget.Toolbar
              android:id="@+id/my_toolbar"
              android:layout_width="match_parent"
              android:layout_height="?attr/actionBarSize"
              android:background="?attr/colorPrimary"
              android:elevation="4dp"
              app:labelVisibilityMode="labeled"
              android:visibility="gone"
              android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
              app:theme="@style/ActionBarThemeOverlay"
              app:popupTheme="@style/ActionBarThemeOverlay"
              app:titleTextColor="@android:color/white"

              >
            </androidx.appcompat.widget.Toolbar>


              <fragment
              android:id="@+id/nav_host_fragment_activity_main"
              android:name="androidx.navigation.fragment.NavHostFragment"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
                  android:layout_centerHorizontal="true"
              android:layout_below="@id/my_toolbar"
                  android:layout_above="@+id/nav_view"
              app:defaultNavHost="true"
              app:navGraph="@navigation/mobile_navigation" />



               <com.google.android.material.bottomnavigation.BottomNavigationView
                  android:id="@+id/nav_view"
                  android:layout_width="match_parent"
                  android:layout_height="@dimen/_50sdp"
                  android:layout_alignParentBottom="true"
                  android:background="#f5f5f5"
                  android:visibility="visible"
                  app:itemIconSize="22dp"
                  app:itemPaddingBottom="@dimen/_8sdp"
                  app:itemRippleColor="@null"
                  app:itemTextColor="@color/bottom_nav_color"
                  app:elevation="4dp"
                  app:itemIconTint="@color/bottom_nav_color"
                  app:labelVisibilityMode="labeled"
                  app:menu="@menu/bottom_nav_menu"
                  app:itemTextAppearanceActive="@style/BottomNavTextAppearance"
                  app:itemTextAppearanceInactive="@style/BottomNavTextAppearance" />



        </RelativeLayout>



</androidx.drawerlayout.widget.DrawerLayout>
