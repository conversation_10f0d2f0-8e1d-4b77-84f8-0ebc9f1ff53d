<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.stripe:stripe-3ds2-android:2.7.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\assets"><file name="ds-amex.pem" path="C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\assets\ds-amex.pem"/><file name="ds-discover.cer" path="C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\assets\ds-discover.cer"/><file name="ds-mastercard.crt" path="C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\assets\ds-mastercard.crt"/><file name="ds-test-ec.txt" path="C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\assets\ds-test-ec.txt"/><file name="ds-test-rsa.txt" path="C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\assets\ds-test-rsa.txt"/><file name="ds-visa.crt" path="C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\assets\ds-visa.crt"/></source></dataSet><dataSet config="com.stripe:stripe-android:14.2.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\assets"><file name="au_becs_bsb.json" path="C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\assets\au_becs_bsb.json"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>