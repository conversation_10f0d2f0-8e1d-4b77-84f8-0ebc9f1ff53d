<?xml version="1.0" encoding="utf-8"?>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.manaknight.app.ui.fragments.alerts.AlertsFragment">
    
 
        <TextView
            android:id="@+id/textView20"
            style="@style/h2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_20sdp"
            android:text="Alerts"
            android:textSize="26sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    
        <TextView
            android:id="@+id/textView21"
            style="@style/subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="Notification"
            android:textColor="@color/gray"
            app:layout_constraintStart_toStartOf="@+id/textView20"
            app:layout_constraintTop_toBottomOf="@+id/textView20" />
    
        <LinearLayout
            android:id="@+id/llNoAlerts"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="@dimen/_50sdp"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView21">
    
            <ImageView
                android:layout_width="@dimen/_60sdp"
                android:layout_height="@dimen/_60sdp"
                android:src="@android:drawable/ic_menu_info_details"
                tools:ignore="ContentDescription" />
    
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:text="No Notification"
                android:textAlignment="center"
                android:textColor="#444484" />
    
        </LinearLayout>
    
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcvNewAlerts"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_25sdp"
            android:paddingTop="@dimen/_10sdp"
            android:paddingBottom="@dimen/_5sdp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView21"
            tools:itemCount="2"
            tools:listitem="@layout/item_app_alert" />
    
        <TextView
            android:id="@+id/tvTagNew"
            style="@style/small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_25sdp"
            android:paddingHorizontal="@dimen/_10sdp"
            android:paddingVertical="5dp"
            android:text="NEW"
            android:textColor="@color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/rcvNewAlerts"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView21" />
    

    
    
    </androidx.constraintlayout.widget.ConstraintLayout>

    

    