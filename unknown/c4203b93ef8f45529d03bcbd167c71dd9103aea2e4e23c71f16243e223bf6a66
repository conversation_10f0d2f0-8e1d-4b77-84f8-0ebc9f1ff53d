<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_15sdp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/_16sdp"
        app:cardElevation="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"

            android:paddingBottom="@dimen/_10sdp">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:paddingHorizontal="@dimen/_15sdp"
                android:text="If you have square foot costs that you estimate regularly, add them here to have quick access while estimating."
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866" />


            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:background="#E2E4E9"/>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                >

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnGotIt"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:paddingVertical="0dp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginRight="19sp"
                    android:layout_alignParentEnd="true"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/black"
                    android:text="Got It" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSkip"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:paddingVertical="0dp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginRight="19sp"
                    android:textColor="#525866"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    android:layout_toStartOf="@+id/btnGotIt"
                    app:cornerRadius="8dp"
                    app:strokeWidth="2dp"
                    app:strokeColor="#525866"
                    app:backgroundTint="@color/white"
                    android:text="Skip" />

            </RelativeLayout>
        </LinearLayout>



    </androidx.cardview.widget.CardView>

</FrameLayout>