<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialogCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"

            android:paddingBottom="10dp">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:paddingHorizontal="15dp"
                android:text="If you have lineal foot costs that you estimate regularly, add them here to have quick access while estimating."
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:textColor="#525866" />


            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="15dp"
                android:background="#E2E4E9"/>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                >

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnGotIt"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_marginTop="10dp"
                    android:paddingVertical="0dp"
                    android:layout_marginRight="19sp"
                    android:layout_alignParentEnd="true"
                    android:textColor="@color/white"
                    app:cornerRadius="8dp"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    app:backgroundTint="@color/black"
                    android:text="Got It" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSkip"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="19sp"
                    android:paddingVertical="0dp"
                    android:textColor="#525866"
                    android:layout_toStartOf="@+id/btnGotIt"
                    android:textSize="14sp"
                    android:fontFamily="@font/inter"
                    android:textFontWeight="500"
                    app:cornerRadius="8dp"
                    app:strokeWidth="2dp"
                    app:strokeColor="#525866"
                    app:backgroundTint="@color/white"
                    android:text="Skip" />

            </RelativeLayout>
        </LinearLayout>



    </androidx.cardview.widget.CardView>

</FrameLayout>