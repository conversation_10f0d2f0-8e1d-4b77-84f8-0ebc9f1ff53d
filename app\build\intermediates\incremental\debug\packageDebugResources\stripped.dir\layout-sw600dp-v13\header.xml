<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"

    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingVertical="5dp"
        android:paddingTop="0dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            >

            <TextView
                android:id="@+id/add_plater_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Sign In"
                android:maxLines="1"
                android:textSize="15sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:gravity="center" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/backButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:padding="10dp"
            android:src="@drawable/icon_left"
            android:visibility="visible"/>



        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSave"
            android:layout_width="wrap_content"
            android:layout_height="40sp"
            android:paddingVertical="0dp"
            android:layout_alignParentEnd="true"
            android:textColor="#375DFB"
            android:textSize="16sp"
            android:fontFamily="@font/inter"
            android:textFontWeight="500"
            android:layout_centerVertical="true"
            app:cornerRadius="8dp"
            app:strokeWidth="2dp"
            android:layout_marginRight="10dp"
            app:strokeColor="#375DFB"
            app:backgroundTint="@color/white"
            android:text="Save"
            android:visibility="invisible"/>
    </RelativeLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="0dp"
            android:background="#D3D3D3"
            />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>