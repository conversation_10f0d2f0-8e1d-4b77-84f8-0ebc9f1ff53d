<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/headerInclude"
        layout="@layout/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="500dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerInclude"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvUserName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter"
                    android:text="UserName"
                    android:textAlignment="center"
                    android:textColor="#000"
                    android:textFontWeight="500"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvUserEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:alpha="0.8"
                    android:fontFamily="@font/inter"
                    android:text="UserName"
                    android:textAlignment="center"
                    android:textColor="#3C3C43"
                    android:textFontWeight="400"
                    android:textSize="12sp" />

                <LinearLayout
                    android:id="@+id/line1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/user_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="8dp"
                        android:fontFamily="@font/inter"
                        android:text="Profile"
                        android:textColor="#000"
                        android:textFontWeight="400"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/line2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/setting_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="8dp"
                        android:fontFamily="@font/inter"
                        android:text="Settings"
                        android:textColor="#000"
                        android:textFontWeight="400"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/line3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/subscription_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="8dp"
                        android:fontFamily="@font/inter"
                        android:text="Plan and Billing"
                        android:textColor="#000"
                        android:textFontWeight="400"
                        android:textSize="14sp" />

                </LinearLayout>


                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:background="#E2E4E9" />

                <LinearLayout
                    android:id="@+id/line4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/logout_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="8dp"
                        android:fontFamily="@font/inter"
                        android:text="Log out"
                        android:textColor="#000"
                        android:textFontWeight="400"
                        android:textSize="14sp" />

                </LinearLayout>
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="40dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="20dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="25dp"
                            android:layout_weight="1"
                            android:alpha="0.8"
                            android:fontFamily="@font/inter"
                            android:text="Terms and Conditions"
                            android:textAlignment="center"
                            android:textColor="#3C3C43"
                            android:textFontWeight="400"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="25dp"
                            android:layout_marginLeft="8dp"
                            android:layout_weight="1"
                            android:alpha="0.8"
                            android:fontFamily="@font/inter"
                            android:text="Privacy Policy"
                            android:textAlignment="center"
                            android:textColor="#3C3C43"
                            android:textFontWeight="400"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:alpha="0.8"
                        android:fontFamily="@font/inter"
                        android:text="v.1.20"
                        android:textAlignment="center"
                        android:textColor="#3C3C43"
                        android:textFontWeight="400"
                        android:textSize="12sp" />
                </LinearLayout>


            </RelativeLayout>

        </LinearLayout>
    </RelativeLayout>




</androidx.constraintlayout.widget.ConstraintLayout>
