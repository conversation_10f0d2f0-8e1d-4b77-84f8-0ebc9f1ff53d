<?xml version="1.0" encoding="utf-8"?>
    
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="15dp"
        android:paddingVertical="10dp"
                android:background="@drawable/rounded_edittext"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">



        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/txtSalePrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Total Sale Price"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtSalePrice_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <TextView
                android:id="@+id/txtProfitOverhead"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Total Profit Overhead"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtProfitOverhead_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <TextView
                android:id="@+id/txtMaterialBudget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Total Material budget"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtMaterialBudget_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <TextView
                android:id="@+id/txtLaboutBudget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Total Labor Budget"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="400"
                android:layout_centerVertical="true"
                android:textColor="#525866"/>

            <TextView
                android:id="@+id/txtLaboutBudget_1"
                android:textAlignment="textEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="Kapil Kumar"
                android:textSize="14sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="500"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="#000000"/>

        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="8dp"

            android:orientation="horizontal">


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:textColor="@color/black"
                app:strokeColor="@color/black"
                app:strokeWidth="1dp"
                app:backgroundTint="@color/white"
                android:textSize="16sp"
                android:fontFamily="@font/inter"
                android:textFontWeight="600"
                android:layout_weight="1"
                app:cornerRadius="8dp"
                android:text="Save As Draft" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnContinue"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:paddingVertical="0dp"
                android:layout_marginLeft="10dp"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:layout_weight="1"

                android:fontFamily="@font/inter"
                android:textFontWeight="600"
                app:cornerRadius="8dp"
                app:backgroundTint="@color/black"
                android:text="Next: Draws" />


        </LinearLayout>




    </LinearLayout>



    
    </androidx.constraintlayout.widget.ConstraintLayout>
    
    