<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="simple_chat_view_widget" modulePackage="Manaknight" filePath="app\src\main\res\layout\simple_chat_view_widget.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/simpleChatView"><Targets><Target id="@+id/simpleChatView" tag="layout/simple_chat_view_widget_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="4" endLine="110" endOffset="55"/></Target><Target id="@+id/rvChats" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="7" startOffset="8" endLine="14" endOffset="55"/></Target><Target id="@+id/moreLayout" view="androidx.appcompat.widget.LinearLayoutCompat"><Expressions/><location startLine="17" startOffset="8" endLine="56" endOffset="54"/></Target><Target id="@+id/imgImage" view="ImageView"><Expressions/><location startLine="27" startOffset="12" endLine="34" endOffset="62"/></Target><Target id="@+id/imgVideo" view="ImageView"><Expressions/><location startLine="36" startOffset="12" endLine="44" endOffset="70"/></Target><Target id="@+id/imgCamera" view="ImageView"><Expressions/><location startLine="46" startOffset="12" endLine="54" endOffset="67"/></Target><Target id="@+id/layoutChatInputHolder" view="LinearLayout"><Expressions/><location startLine="58" startOffset="8" endLine="94" endOffset="22"/></Target><Target id="@+id/btnAdd" view="ImageView"><Expressions/><location startLine="71" startOffset="12" endLine="80" endOffset="60"/></Target><Target id="@+id/edtMessage" view="EditText"><Expressions/><location startLine="82" startOffset="12" endLine="92" endOffset="39"/></Target><Target id="@+id/btnSend" view="ImageView"><Expressions/><location startLine="96" startOffset="8" endLine="107" endOffset="55"/></Target></Targets></Layout>