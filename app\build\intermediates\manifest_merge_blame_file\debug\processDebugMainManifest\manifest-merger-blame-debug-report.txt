1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.manaknight.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:5-76
11-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:22-74
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:5-66
12-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:22-63
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:5-67
13-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:5-79
14-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:22-76
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:5-79
15-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:5-81
16-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:22-78
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:5-76
17-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:22-73
18    <uses-permission android:name="android.permission.CAMERA" />
18-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:5-65
18-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:22-62
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:5-80
19-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:22-78
20    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
20-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:5-79
20-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:22-76
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:5-68
21-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:22-65
22    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
22-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:5-79
22-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:22-76
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:5-81
23-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:22-78
24    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:5-80
24-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:22-77
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:5-81
25-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:22-79
26    <uses-permission android:name="com.android.vending.BILLING" />
26-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:5-67
26-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:22-64
27
28    <!-- Biometric Authentication (Face ID) Permissions -->
29    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
29-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:24:5-72
29-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:24:22-69
30    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
30-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:25:5-74
30-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:25:22-71
31
32    <queries>
32-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:30:5-32:15
33        <package android:name="com.facebook.katana" />
33-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:31:9-55
33-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:31:18-52
34        <!-- Needs to be explicitly declared on Android R+ -->
35        <package android:name="com.google.android.apps.maps" />
35-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
35-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
36
37        <intent>
37-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:23:9-25:18
38            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
38-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:13-86
38-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:21-83
39        </intent>
40        <intent>
40-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:13:9-15:18
41            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
41-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:13-91
41-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:21-88
42        </intent>
43    </queries>
44
45    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
45-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:5-76
45-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:22-73
46
47    <uses-feature
47-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
48        android:glEsVersion="0x00020000"
48-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:27:9-41
49        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
49-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:28:9-32
50    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
50-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:5-82
50-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:22-79
51    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
51-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
51-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
52    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
52-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
52-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
53
54    <permission
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
59
60    <application
60-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:34:5-88:19
61        android:name="com.manaknight.app.App"
61-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:35:9-46
62        android:allowBackup="false"
62-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:36:9-36
63        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
63-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
64        android:dataExtractionRules="@xml/data_extraction_rules"
64-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:37:9-65
65        android:debuggable="true"
66        android:extractNativeLibs="false"
67        android:fullBackupContent="@xml/backup_rules"
67-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:38:9-54
68        android:icon="@mipmap/ic_launcher"
68-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:39:9-43
69        android:label="@string/app_name"
69-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:40:9-41
70        android:requestLegacyExternalStorage="true"
70-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:43:9-52
71        android:roundIcon="@mipmap/ic_launcher_round"
71-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:41:9-54
72        android:supportsRtl="true"
72-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:42:9-35
73        android:theme="@style/AppTheme"
73-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:44:9-40
74        android:usesCleartextTraffic="true" >
74-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:45:9-44
75        <activity
75-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:47:9-64:20
76            android:name="com.manaknight.app.MainActivity"
76-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:48:13-59
77            android:exported="true"
77-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:49:13-36
78            android:hardwareAccelerated="true"
78-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:53:13-47
79            android:launchMode="singleTask"
79-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:54:13-44
80            android:screenOrientation="portrait"
80-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:50:13-49
81            android:windowSoftInputMode="adjustResize" >
81-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:52:13-55
82            <intent-filter>
82-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:55:13-59:29
83                <action android:name="android.intent.action.MAIN" />
83-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:56:17-69
83-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:56:25-66
84
85                <category android:name="android.intent.category.LAUNCHER" />
85-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:58:17-77
85-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:58:27-74
86            </intent-filter>
87
88            <meta-data
88-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:61:13-63:36
89                android:name="android.app.lib_name"
89-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:62:17-52
90                android:value="" />
90-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:63:17-33
91        </activity>
92
93        <meta-data
93-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:66:9-68:58
94            android:name="com.google.firebase.messaging.default_notification_icon"
94-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:67:13-83
95            android:resource="@drawable/ic_loc_active" />
95-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:68:13-55
96
97        <!-- <provider -->
98        <!-- android:name="androidx.core.content.FileProvider" -->
99        <!-- android:authorities="com.manaknight.app.provider" -->
100        <!-- android:exported="false" -->
101        <!-- android:grantUriPermissions="true"> -->
102        <!-- <meta-data -->
103        <!-- android:name="android.support.FILE_PROVIDER_PATHS" -->
104        <!-- android:resource="@xml/file_provider" /> -->
105        <!-- </provider> -->
106
107        <service
107-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:80:9-87:19
108            android:name="com.manaknight.app.fcm.MyFirebasePushNotifications"
108-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:13-78
109            android:exported="false" >
109-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:82:13-37
110            <intent-filter>
110-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:84:13-86:29
111                <action android:name="com.google.firebase.MESSAGING_EVENT" />
111-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:17-78
111-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:25-75
112            </intent-filter>
113        </service>
114
115        <activity
115-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:12:9-14:57
116            android:name="com.stripe.android.view.AddPaymentMethodActivity"
116-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:13:13-76
117            android:theme="@style/StripeDefaultTheme" />
117-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:14:13-54
118        <activity
118-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:15:9-17:57
119            android:name="com.stripe.android.view.PaymentMethodsActivity"
119-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:16:13-74
120            android:theme="@style/StripeDefaultTheme" />
120-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:17:13-54
121        <activity
121-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:18:9-20:57
122            android:name="com.stripe.android.view.PaymentFlowActivity"
122-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:19:13-71
123            android:theme="@style/StripeDefaultTheme" />
123-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:20:13-54
124        <activity
124-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:21:9-23:57
125            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
125-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:22:13-78
126            android:theme="@style/StripeDefaultTheme" />
126-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:23:13-54
127        <activity
127-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:24:9-26:57
128            android:name="com.stripe.android.view.PaymentRelayActivity"
128-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:25:13-72
129            android:theme="@style/StripeDefaultTheme" />
129-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:26:13-54
130        <activity
130-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:27:9-29:57
131            android:name="com.stripe.android.view.Stripe3ds2CompletionActivity"
131-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:28:13-80
132            android:theme="@style/StripeDefaultTheme" />
132-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:29:13-54
133        <activity
133-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:12:9-14:54
134            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
134-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:13:13-81
135            android:theme="@style/Stripe3DS2Theme" />
135-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:14:13-51
136        <activity
136-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:15:9-17:54
137            android:name="com.stripe.android.stripe3ds2.views.ChallengeProgressDialogActivity"
137-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:16:13-95
138            android:theme="@style/Stripe3DS2Theme" />
138-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:17:13-51
139        <activity
139-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
140            android:name="com.karumi.dexter.DexterActivity"
140-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
141            android:theme="@style/Dexter.Internal.Theme.Transparent" />
141-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
142        <activity
142-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:15:9-21:20
143            android:name="com.google.android.libraries.places.widget.AutocompleteActivity"
143-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:16:13-91
144            android:exported="false"
144-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:17:13-37
145            android:label="@string/places_autocomplete_label"
145-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:18:13-62
146            android:theme="@style/PlacesAutocompleteOverlay"
146-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:19:13-61
147            android:windowSoftInputMode="adjustResize" >
147-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:20:13-55
148        </activity>
149        <activity
149-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:23:9-27:75
150            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
150-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:24:13-93
151            android:excludeFromRecents="true"
151-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:25:13-46
152            android:exported="false"
152-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:26:13-37
153            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
153-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:27:13-72
154        <!--
155            Service handling Google Sign-In user revocation. For apps that do not integrate with
156            Google Sign-In, this service will never be started.
157        -->
158        <service
158-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:33:9-37:51
159            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
159-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:34:13-89
160            android:exported="true"
160-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:35:13-36
161            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
161-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:36:13-107
162            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
162-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:37:13-48
163        <uses-library
163-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
164            android:name="org.apache.http.legacy"
164-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:40:13-50
165            android:required="false" />
165-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:41:13-37
166
167        <meta-data
167-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:23:9-25:38
168            android:name="com.google.gms.fitness.sdk.version"
168-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:24:13-62
169            android:value="20.0.0" />
169-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:25:13-35
170
171        <service
171-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:9:9-15:19
172            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
172-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:10:13-91
173            android:directBootAware="true"
173-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:17:13-43
174            android:exported="false" >
174-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:11:13-37
175            <meta-data
175-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:12:13-14:85
176                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
176-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:13:17-114
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:14:17-82
178            <meta-data
178-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:12:13-14:85
179                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
179-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:13:17-124
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:14:17-82
181            <meta-data
181-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:20:13-22:85
182                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
182-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:21:17-120
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:22:17-82
184        </service>
185
186        <provider
186-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:9:9-13:38
187            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
187-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:10:13-78
188            android:authorities="com.manaknight.app.mlkitinitprovider"
188-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:11:13-69
189            android:exported="false"
189-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:12:13-37
190            android:initOrder="99" />
190-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:13:13-35
191
192        <service
192-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:8:9-14:19
193            android:name="com.google.firebase.components.ComponentDiscoveryService"
193-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:9:13-84
194            android:directBootAware="true"
194-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:34:13-43
195            android:exported="false" >
195-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:10:13-37
196            <meta-data
196-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:11:13-13:85
197                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthKtxRegistrar"
197-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:12:17-116
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:13:17-82
199            <meta-data
199-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:67:13-69:85
200                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
200-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:68:17-109
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:69:17-82
202            <meta-data
202-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:28:13-30:85
203                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar"
203-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:29:17-126
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:30:17-82
205            <meta-data
205-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:55:13-57:85
206                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
206-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:56:17-119
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:57:17-82
208            <meta-data
208-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:26:13-28:85
209                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
209-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:27:17-130
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:28:17-82
211            <meta-data
211-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:11:13-13:85
212                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar"
212-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:12:17-126
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:13:17-82
214            <meta-data
214-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:14:13-16:85
215                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonKtxRegistrar"
215-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:15:17-113
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:16:17-82
217            <meta-data
217-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:17:13-19:85
218                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
218-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:18:17-115
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:19:17-82
220            <meta-data
220-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
221                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
221-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
223            <meta-data
223-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:17:13-19:85
224                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
224-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:18:17-127
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:19:17-82
226            <meta-data
226-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
227                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
227-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
229        </service>
230
231        <activity
231-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:27:9-44:20
232            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
232-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:28:13-80
233            android:excludeFromRecents="true"
233-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:29:13-46
234            android:exported="true"
234-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:30:13-36
235            android:launchMode="singleTask"
235-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:31:13-44
236            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
236-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:32:13-72
237            <intent-filter>
237-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:33:13-43:29
238                <action android:name="android.intent.action.VIEW" />
238-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:17-69
238-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:25-66
239
240                <category android:name="android.intent.category.DEFAULT" />
240-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:17-76
240-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:27-73
241                <category android:name="android.intent.category.BROWSABLE" />
241-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:17-78
241-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:27-75
242
243                <data
243-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:39:17-42:51
244                    android:host="firebase.auth"
244-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:40:21-49
245                    android:path="/"
245-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:41:21-37
246                    android:scheme="genericidp" />
246-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:42:21-48
247            </intent-filter>
248        </activity>
249        <activity
249-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:45:9-62:20
250            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
250-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:46:13-79
251            android:excludeFromRecents="true"
251-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:47:13-46
252            android:exported="true"
252-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:48:13-36
253            android:launchMode="singleTask"
253-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:49:13-44
254            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
254-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:50:13-72
255            <intent-filter>
255-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:51:13-61:29
256                <action android:name="android.intent.action.VIEW" />
256-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:17-69
256-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:25-66
257
258                <category android:name="android.intent.category.DEFAULT" />
258-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:17-76
258-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:27-73
259                <category android:name="android.intent.category.BROWSABLE" />
259-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:17-78
259-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:27-75
260
261                <data
261-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:39:17-42:51
262                    android:host="firebase.auth"
262-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:40:21-49
263                    android:path="/"
263-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:41:21-37
264                    android:scheme="recaptcha" />
264-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:42:21-48
265            </intent-filter>
266        </activity>
267
268        <receiver
268-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:31:9-38:20
269            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
269-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:32:13-78
270            android:exported="true"
270-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:33:13-36
271            android:permission="com.google.android.c2dm.permission.SEND" >
271-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:34:13-73
272            <intent-filter>
272-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:35:13-37:29
273                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
273-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:17-81
273-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:25-78
274            </intent-filter>
275        </receiver>
276        <!--
277             FirebaseMessagingService performs security checks at runtime,
278             but set to not exported to explicitly avoid allowing another app to call it.
279        -->
280        <service
280-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:44:9-51:19
281            android:name="com.google.firebase.messaging.FirebaseMessagingService"
281-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:45:13-82
282            android:directBootAware="true"
282-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:46:13-43
283            android:exported="false" >
283-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:47:13-37
284            <intent-filter android:priority="-500" >
284-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:84:13-86:29
285                <action android:name="com.google.firebase.MESSAGING_EVENT" />
285-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:17-78
285-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:25-75
286            </intent-filter>
287        </service>
288
289        <activity
289-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
290            android:name="com.google.android.gms.common.api.GoogleApiActivity"
290-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
291            android:exported="false"
291-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
292            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
292-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
293
294        <provider
294-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:25:9-30:39
295            android:name="com.google.firebase.provider.FirebaseInitProvider"
295-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:26:13-77
296            android:authorities="com.manaknight.app.firebaseinitprovider"
296-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:27:13-72
297            android:directBootAware="true"
297-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:28:13-43
298            android:exported="false"
298-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:29:13-37
299            android:initOrder="100" />
299-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:30:13-36
300
301        <uses-library
301-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:29:9-31:40
302            android:name="androidx.camera.extensions.impl"
302-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:30:13-59
303            android:required="false" />
303-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:31:13-37
304
305        <service
305-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:24:9-33:19
306            android:name="androidx.camera.core.impl.MetadataHolderService"
306-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:25:13-75
307            android:enabled="false"
307-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:26:13-36
308            android:exported="false" >
308-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:27:13-37
309            <meta-data
309-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:30:13-32:89
310                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
310-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:31:17-103
311                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
311-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:32:17-86
312        </service>
313
314        <receiver
314-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
315            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
315-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
316            android:enabled="true"
316-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
317            android:exported="false" >
317-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
318        </receiver>
319
320        <service
320-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
321            android:name="com.google.android.gms.measurement.AppMeasurementService"
321-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
322            android:enabled="true"
322-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
323            android:exported="false" />
323-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
324        <service
324-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
325            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
325-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
326            android:enabled="true"
326-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
327            android:exported="false"
327-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
328            android:permission="android.permission.BIND_JOB_SERVICE" />
328-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
329
330        <activity
330-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
331            android:name="androidx.compose.ui.tooling.PreviewActivity"
331-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
332            android:exported="true" />
332-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
333
334        <provider
334-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
335            android:name="androidx.startup.InitializationProvider"
335-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
336            android:authorities="com.manaknight.app.androidx-startup"
336-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
337            android:exported="false" >
337-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
338            <meta-data
338-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
339                android:name="androidx.emoji2.text.EmojiCompatInitializer"
339-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
340                android:value="androidx.startup" />
340-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
341            <meta-data
341-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
342                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
342-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
343                android:value="androidx.startup" />
343-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
344            <meta-data
344-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
345                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
345-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
346                android:value="androidx.startup" />
346-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
347        </provider>
348
349        <uses-library
349-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
350            android:name="androidx.window.extensions"
350-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
351            android:required="false" />
351-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
352        <uses-library
352-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
353            android:name="androidx.window.sidecar"
353-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
354            android:required="false" />
354-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
355
356        <meta-data
356-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
357            android:name="com.google.android.gms.version"
357-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
358            android:value="@integer/google_play_services_version" />
358-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
359
360        <receiver
360-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
361            android:name="androidx.profileinstaller.ProfileInstallReceiver"
361-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
362            android:directBootAware="false"
362-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
363            android:enabled="true"
363-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
364            android:exported="true"
364-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
365            android:permission="android.permission.DUMP" >
365-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
366            <intent-filter>
366-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
367                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
368            </intent-filter>
369            <intent-filter>
369-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
370                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
371            </intent-filter>
372            <intent-filter>
372-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
373                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
374            </intent-filter>
375            <intent-filter>
375-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
376                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
376-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
376-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
377            </intent-filter>
378        </receiver>
379
380        <meta-data
380-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:19:9-21:37
381            android:name="com.google.android.play.billingclient.version"
381-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:20:13-73
382            android:value="6.0.1" />
382-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:21:13-34
383
384        <activity
384-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:23:9-27:75
385            android:name="com.android.billingclient.api.ProxyBillingActivity"
385-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:24:13-78
386            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
386-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:25:13-96
387            android:exported="false"
387-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:26:13-37
388            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
388-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:27:13-72
389
390        <service
390-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
391            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
391-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
392            android:exported="false" >
392-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
393            <meta-data
393-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
394                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
394-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
395                android:value="cct" />
395-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
396        </service>
397        <service
397-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
398            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
398-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
399            android:exported="false"
399-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
400            android:permission="android.permission.BIND_JOB_SERVICE" >
400-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
401        </service>
402
403        <receiver
403-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
404            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
404-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
405            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
405-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
406        <activity
406-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:14:9-18:65
407            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
407-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:15:13-93
408            android:exported="false"
408-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:16:13-37
409            android:stateNotNeeded="true"
409-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:17:13-42
410            android:theme="@style/Theme.PlayCore.Transparent" />
410-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:18:13-62
411    </application>
412
413</manifest>
